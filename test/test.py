import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from nonebot_plugin_kfcrazy import KFC

async def test_api_response():
    """测试API响应格式"""
    import httpx
    import json

    kfc = KFC()
    print("=== 测试API响应格式 ===")

    # 测试获取cookie
    print("\n0. 测试获取cookie...")
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.post(url=kfc.url_session, headers=kfc.headers)
            print(f"Session API状态码: {resp.status_code}")
            print(f"Session API响应: {resp.text[:200]}...")
            print(f"Session API cookies: {dict(resp.cookies)}")

        cookie = await kfc.get_cookie()
        print(f"获取到cookie: {cookie[:50] if cookie else 'None'}...")
    except Exception as e:
        print(f"❌ 获取cookie失败: {e}")
        import traceback
        traceback.print_exc()

    # 测试城市API（带cookie）
    print("\n1. 测试城市API响应（带cookie）...")
    try:
        cookie = await kfc.get_cookie()
        headers_with_cookie = kfc.headers.copy()
        if cookie:
            headers_with_cookie['Cookie'] = f'koa.sid={cookie}'

        async with httpx.AsyncClient() as client:
            resp = await client.post(
                url=kfc.url_city,
                headers=headers_with_cookie,
            )
            print(f"状态码: {resp.status_code}")
            resp_json = json.loads(resp.text)
            print(f"响应内容: {json.dumps(resp_json, indent=2, ensure_ascii=False)[:500]}...")
    except Exception as e:
        print(f"❌ 城市API测试失败: {e}")

    # 测试地理位置API
    print("\n2. 测试地理位置API响应...")
    try:
        params = {
            'address': '无锡',
            'key': kfc.key
        }
        async with httpx.AsyncClient() as client:
            resp = await client.get(
                url=kfc.tencent_map,
                headers=kfc.headers,
                params=params
            )
            print(f"状态码: {resp.status_code}")
            resp_json = json.loads(resp.text)
            print(f"响应内容: {json.dumps(resp_json, indent=2, ensure_ascii=False)[:500]}...")
    except Exception as e:
        print(f"❌ 地理位置API测试失败: {e}")

async def test_kfc_api():
    """测试 KFC 类的各种接口"""
    kfc = KFC()

    print("=== 测试 KFC API 接口 ===")

    # 测试1: 获取城市ID
    print("\n1. 测试获取城市ID...")
    try:
        await kfc.get_city_id("无锡")
        from nonebot_plugin_kfcrazy import CityCode
        if CityCode:
            print(f"✅ 获取城市ID成功: {CityCode}")
        else:
            print("❌ 获取城市ID失败: CityCode为空")
    except Exception as e:
        print(f"❌ 获取城市ID失败: {e}")
        import traceback
        traceback.print_exc()

    # 测试2: 获取地理位置
    print("\n2. 测试获取地理位置...")
    try:
        await kfc.get_location("无锡")
        from nonebot_plugin_kfcrazy import Lng, Lat
        if Lng and Lat:
            print(f"✅ 获取地理位置成功: 经度={Lng}, 纬度={Lat}")
        else:
            print("❌ 获取地理位置失败: 经纬度为空")
    except Exception as e:
        print(f"❌ 获取地理位置失败: {e}")
        import traceback
        traceback.print_exc()

    # 测试3: 搜索店铺
    print("\n3. 测试搜索店铺...")
    try:
        # 先获取城市信息
        await kfc.get_city_id("无锡")
        from nonebot_plugin_kfcrazy import CityCode, DistrictCode, Lng, Lat

        if CityCode:
            store_list, store_ids = await kfc.search_store(
                keyword="万达",
                city_code=CityCode,
                district_code=DistrictCode,
                mylng=str(Lng),
                mylat=str(Lat)
            )
            print("✅ 搜索店铺成功")
            print(f"找到 {len(store_ids)} 家店铺")
            print(store_list[:200] + "..." if len(store_list) > 200 else store_list)

            # 测试4: 获取菜单
            if store_ids:
                print("\n4. 测试获取菜单...")
                try:
                    menu_list, menu_detail = await kfc.get_menu(store_ids[0])
                    print("✅ 获取菜单成功")
                    print(f"菜单分类数量: {len(menu_detail)}")
                    print(menu_list[:200] + "..." if len(menu_list) > 200 else menu_list)

                    # 测试5: 获取食物详情
                    if menu_detail:
                        print("\n5. 测试获取食物详情...")
                        try:
                            food_result = await kfc.get_food(menu_detail, 0, use_nonebot=False)
                            print("✅ 获取食物详情成功")
                            if isinstance(food_result, dict):
                                print(f"食物文本长度: {len(food_result['text'])}")
                                print(f"图片数量: {len(food_result['images'])}")
                                print("食物信息预览:")
                                print(food_result['text'][:300] + "..." if len(food_result['text']) > 300 else food_result['text'])
                        except Exception as e:
                            print(f"❌ 获取食物详情失败: {e}")
                except Exception as e:
                    print(f"❌ 获取菜单失败: {e}")
        else:
            print("❌ 未能获取城市代码，跳过店铺搜索")
    except Exception as e:
        print(f"❌ 搜索店铺失败: {e}")

if __name__ == '__main__':
    print("开始测试 KFC 类接口...")
    asyncio.run(test_api_response())
    print("\n" + "="*50)
    asyncio.run(test_kfc_api())
    print("\n测试完成！")